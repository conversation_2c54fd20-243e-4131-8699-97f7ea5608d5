#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示增强的Discogs API客户端功能
"""

import time
import json
from api_incremental_release_fetcher import EnhancedDiscogsAPIClient, save_progress, load_progress

def demo_enhanced_features():
    """演示增强功能"""
    print("🚀 演示增强的Discogs API客户端功能")
    print("="*60)
    
    # 创建增强的API客户端
    print("1. 初始化增强API客户端...")
    client = EnhancedDiscogsAPIClient(use_proxy=False)
    
    # 演示账号轮换和智能重试
    print("\n2. 测试API调用和账号轮换...")
    test_ids = [249504, 1, 999999999, 2, 3]  # 包含存在和不存在的ID
    
    for release_id in test_ids:
        print(f"\n📞 测试 Release ID: {release_id}")
        result = client.get_release(release_id)
        
        if isinstance(result, dict):
            print(f"✅ 成功获取: {result.get('title', 'Unknown')[:50]}...")
        elif result is None:
            print("⏭️ 404 - Release不存在")
        elif result == "429":
            print("⏳ 429 - 被限流")
        else:
            print(f"❌ 请求失败: {result}")
        
        # 显示当前统计
        stats = client.get_stats()
        print(f"📊 当前统计: 请求 {stats['total_requests']}, "
              f"成功率 {stats['success_rate']:.1%}, "
              f"延迟 {stats['current_delay']:.1f}s")
        
        time.sleep(1)  # 避免过快请求
    
    # 演示进度保存和加载
    print("\n3. 演示进度保存和加载...")
    
    # 保存进度
    test_stats = {
        'processed': len(test_ids),
        'success': 3,
        'not_found_404': 1,
        'rate_limited_429': 0,
        'api_failed': 0,
        'db_failed': 0
    }
    
    api_stats = client.get_stats()
    save_progress(12345, test_stats, api_stats)
    print("💾 进度已保存")
    
    # 加载进度
    progress = load_progress()
    if progress:
        print(f"📂 进度已加载: ID {progress['current_id']}")
        print(f"📊 之前统计: {progress['stats']}")
    
    # 最终统计
    print("\n4. 最终统计信息...")
    final_stats = client.get_stats()
    print("="*60)
    print("📊 API客户端统计")
    print("="*60)
    print(f"📞 总请求数: {final_stats['total_requests']}")
    print(f"✅ 总成功率: {final_stats['success_rate']:.1%}")
    print(f"⏱️ 当前延迟: {final_stats['current_delay']:.1f}s")
    print(f"🔧 基础延迟: {final_stats['base_delay']:.1f}s")
    
    print("\n👥 账号详细统计:")
    for acc_stats in final_stats['accounts']:
        print(f"  👤 {acc_stats['name']}:")
        print(f"     状态: {acc_stats['status']}")
        print(f"     请求: {acc_stats['requests']}")
        print(f"     成功: {acc_stats['success']}")
        print(f"     错误: {acc_stats['errors']}")
        print(f"     成功率: {acc_stats['success_rate']:.1%}")
    
    print("\n🎉 演示完成！")

def demo_usage_example():
    """演示实际使用示例"""
    print("\n" + "="*60)
    print("💡 实际使用示例")
    print("="*60)
    
    print("""
# 基本使用方法：
from api_incremental_release_fetcher import EnhancedDiscogsAPIClient

# 创建客户端
client = EnhancedDiscogsAPIClient(use_proxy=False)

# 获取release数据
result = client.get_release(249504)

if isinstance(result, dict):
    print(f"成功: {result['title']}")
elif result is None:
    print("404 - 不存在")
elif result == "429":
    print("被限流")
else:
    print("请求失败")

# 获取统计信息
stats = client.get_stats()
print(f"成功率: {stats['success_rate']:.1%}")
    """)
    
    print("\n🔧 主要改进功能:")
    print("✅ 双账号自动轮换")
    print("✅ 智能重试机制")
    print("✅ 429错误处理")
    print("✅ 动态延迟调整")
    print("✅ 浏览器模拟")
    print("✅ 断点续传支持")
    print("✅ 详细统计信息")
    print("✅ 进度保存/恢复")

if __name__ == "__main__":
    try:
        demo_enhanced_features()
        demo_usage_example()
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
