#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的Discogs API客户端
"""

import sys
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_account_manager():
    """测试账号管理器"""
    try:
        from api_incremental_release_fetcher import DiscogsAccountManager, DISCOGS_ACCOUNTS, AccountStatus
        
        logger.info("🧪 测试账号管理器...")
        
        # 创建账号管理器
        manager = DiscogsAccountManager(DISCOGS_ACCOUNTS)
        
        # 测试获取活跃账号
        account = manager.get_active_account()
        if account:
            logger.info(f"✅ 获取到活跃账号: {account.name}")
        else:
            logger.error("❌ 无法获取活跃账号")
            return False
            
        # 测试记录请求结果
        manager.record_request_result(account, True, 200)
        manager.record_request_result(account, False, 429)
        
        # 获取统计信息
        stats = manager.get_stats()
        logger.info(f"📊 统计信息: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 账号管理器测试失败: {e}")
        return False

def test_enhanced_api_client():
    """测试增强的API客户端"""
    try:
        from api_incremental_release_fetcher import EnhancedDiscogsAPIClient
        
        logger.info("🧪 测试增强API客户端...")
        
        # 创建客户端
        client = EnhancedDiscogsAPIClient(use_proxy=False)
        
        # 测试获取一个已知存在的release
        logger.info("📞 测试API调用...")
        result = client.get_release(249504)  # Rick Astley - Never Gonna Give You Up
        
        if result and isinstance(result, dict):
            logger.info(f"✅ API调用成功，获取到数据: {result.get('title', 'Unknown')}")
        elif result is None:
            logger.info("ℹ️ API返回404（正常情况）")
        elif result == "429":
            logger.warning("⚠️ API返回429限流")
        else:
            logger.error(f"❌ API调用失败: {result}")
            return False
            
        # 获取客户端统计信息
        stats = client.get_stats()
        logger.info(f"📊 客户端统计: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ API客户端测试失败: {e}")
        return False

def test_progress_functions():
    """测试进度保存和加载功能"""
    try:
        from api_incremental_release_fetcher import save_progress, load_progress
        
        logger.info("🧪 测试进度功能...")
        
        # 测试保存进度
        test_stats = {'processed': 100, 'success': 90, 'errors': 10}
        test_api_stats = {'total_requests': 100, 'success_rate': 0.9}
        
        save_progress(12345, test_stats, test_api_stats)
        logger.info("✅ 进度保存成功")
        
        # 测试加载进度
        progress = load_progress()
        if progress and progress['current_id'] == 12345:
            logger.info("✅ 进度加载成功")
            return True
        else:
            logger.error("❌ 进度加载失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 进度功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始测试增强的API客户端...")
    
    tests = [
        ("账号管理器", test_account_manager),
        ("进度功能", test_progress_functions),
        ("API客户端", test_enhanced_api_client),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
        return True
    else:
        logger.error("💥 部分测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
