#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API改进功能的独立脚本
"""

import time
import json
import requests
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict

# 复制核心类定义进行测试
class AccountStatus(Enum):
    ACTIVE = "active"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class DiscogsAccount:
    """Discogs账号配置类"""
    user_agent: str
    user_token: str
    name: str
    status: AccountStatus = AccountStatus.ACTIVE
    last_request_time: float = 0.0
    request_count: int = 0
    error_count: int = 0
    rate_limit_until: float = 0.0
    success_count: int = 0

# 配置两个Discogs账号
DISCOGS_ACCOUNTS = [
    DiscogsAccount(
        user_agent="ywlstanddata01/1.0 +https://github.com/ywlstanddata01/discogs-fetcher",
        user_token="ksIIevOvWwGWObNIkhkBZYwlCQXOdKwSyilIapZA",
        name="ywlstanddata01"
    ),
    DiscogsAccount(
        user_agent="sunliyu38ywl/1.0 +https://github.com/sunliyu38ywl/discogs-client", 
        user_token="bXNeyeiZvMWBMFeAHTLGmgEwPuMiQQgrWsTehxdr",
        name="sunliyu38ywl"
    )
]

class DiscogsAccountManager:
    """Discogs账号管理器"""
    
    def __init__(self, accounts: List[DiscogsAccount]):
        self.accounts = accounts
        self.current_account_index = 0
        self.total_requests = 0
        self.total_success = 0
        self.total_errors = 0
        
    def get_active_account(self) -> Optional[DiscogsAccount]:
        """获取当前可用的账号"""
        current_time = time.time()
        
        # 检查当前账号是否可用
        current_account = self.accounts[self.current_account_index]
        if self._is_account_available(current_account, current_time):
            return current_account
            
        # 尝试切换到其他可用账号
        for i, account in enumerate(self.accounts):
            if i != self.current_account_index and self._is_account_available(account, current_time):
                print(f"🔄 切换账号: {self.accounts[self.current_account_index].name} -> {account.name}")
                self.current_account_index = i
                return account
                
        # 如果所有账号都不可用，返回错误最少的账号
        best_account = min(self.accounts, key=lambda a: a.error_count)
        if best_account != current_account:
            print(f"⚠️ 所有账号都有问题，选择错误最少的账号: {best_account.name}")
            self.current_account_index = self.accounts.index(best_account)
        
        return best_account
    
    def _is_account_available(self, account: DiscogsAccount, current_time: float) -> bool:
        """检查账号是否可用"""
        # 检查是否被速率限制
        if current_time < account.rate_limit_until:
            return False
            
        # 检查账号状态
        if account.status in [AccountStatus.DISABLED, AccountStatus.ERROR]:
            return False
            
        # 检查错误率（如果错误率过高，暂时禁用）
        if account.request_count > 10 and account.error_count / account.request_count > 0.5:
            return False
            
        return True
    
    def record_request_result(self, account: DiscogsAccount, success: bool, status_code: Optional[int] = None):
        """记录请求结果"""
        account.request_count += 1
        self.total_requests += 1
        
        if success:
            account.success_count += 1
            account.error_count = max(0, account.error_count - 1)  # 成功时减少错误计数
            self.total_success += 1
            if account.status == AccountStatus.ERROR:
                account.status = AccountStatus.ACTIVE
                print(f"✅ 账号 {account.name} 恢复正常")
        else:
            account.error_count += 1
            self.total_errors += 1
            
            if status_code == 429:
                account.status = AccountStatus.RATE_LIMITED
                account.rate_limit_until = time.time() + 60  # 1分钟后重试
                print(f"⏳ 账号 {account.name} 被限流，1分钟后重试")
            elif account.error_count >= 5:
                account.status = AccountStatus.ERROR
                print(f"❌ 账号 {account.name} 错误过多，暂时禁用")
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return {
            'total_requests': self.total_requests,
            'total_success': self.total_success,
            'total_errors': self.total_errors,
            'success_rate': self.total_success / max(1, self.total_requests),
            'accounts': [
                {
                    'name': acc.name,
                    'status': acc.status.value,
                    'requests': acc.request_count,
                    'success': acc.success_count,
                    'errors': acc.error_count,
                    'success_rate': acc.success_count / max(1, acc.request_count)
                }
                for acc in self.accounts
            ]
        }

def test_account_manager():
    """测试账号管理器"""
    print("🧪 测试账号管理器...")
    
    # 创建账号管理器
    manager = DiscogsAccountManager(DISCOGS_ACCOUNTS)
    
    # 测试获取活跃账号
    account = manager.get_active_account()
    if account:
        print(f"✅ 获取到活跃账号: {account.name}")
    else:
        print("❌ 无法获取活跃账号")
        return False
        
    # 测试记录请求结果
    manager.record_request_result(account, True, 200)
    manager.record_request_result(account, False, 429)
    
    # 获取统计信息
    stats = manager.get_stats()
    print(f"📊 统计信息: {stats}")
    
    return True

def test_api_call():
    """测试实际的API调用"""
    print("🧪 测试API调用...")
    
    manager = DiscogsAccountManager(DISCOGS_ACCOUNTS)
    account = manager.get_active_account()
    
    if not account:
        print("❌ 无可用账号")
        return False
    
    # 创建会话
    session = requests.Session()
    headers = {
        'User-Agent': account.user_agent,
        'Authorization': f'Discogs token={account.user_token}',
        'Accept': 'application/vnd.discogs.v2.html+json',
    }
    session.headers.update(headers)
    
    # 测试API调用
    try:
        url = "https://api.discogs.com/releases/249504"  # Rick Astley - Never Gonna Give You Up
        print(f"📞 调用API: {url}")
        print(f"👤 使用账号: {account.name}")
        
        response = session.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功: {data.get('title', 'Unknown')}")
            manager.record_request_result(account, True, 200)
            return True
        elif response.status_code == 404:
            print("ℹ️ API返回404（正常情况）")
            manager.record_request_result(account, True, 404)
            return True
        elif response.status_code == 429:
            print("⚠️ API返回429限流")
            manager.record_request_result(account, False, 429)
            return True
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            manager.record_request_result(account, False, response.status_code)
            return False
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        manager.record_request_result(account, False)
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试API改进功能...")
    print(f"📱 配置的账号数量: {len(DISCOGS_ACCOUNTS)}")
    
    for i, account in enumerate(DISCOGS_ACCOUNTS):
        print(f"  {i+1}. {account.name}")
    
    print("\n" + "="*50)
    
    # 测试账号管理器
    if not test_account_manager():
        print("❌ 账号管理器测试失败")
        return False
    
    print("\n" + "="*50)
    
    # 测试API调用
    if not test_api_call():
        print("❌ API调用测试失败")
        return False
    
    print("\n" + "="*50)
    print("🎉 所有测试通过！")
    return True

if __name__ == "__main__":
    main()
